import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { SqsService, SqsMessage } from './sqs.service';

export interface PollingConfig {
  queueName: string;
  maxMessages?: number;
  waitTimeSeconds?: number;
  pollingInterval?: number;
  autoDelete?: boolean;
}

@Injectable()
export class SqsPollingService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(SqsPollingService.name);
  private pollingIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isPolling: Map<string, boolean> = new Map();

  constructor(private readonly sqsService: SqsService) {}

  onModuleInit() {
    this.logger.log('SQS Polling Service initialized');
  }

  onModuleDestroy() {
    this.stopAllPolling();
  }

  async startPolling(
    config: PollingConfig,
    messageHandler: (messages: SqsMessage[]) => Promise<void>,
  ): Promise<void> {
    const { queueName, maxMessages = 10, waitTimeSeconds = 20, pollingInterval = 1000, autoDelete = true } = config;

    if (this.isPolling.get(queueName)) {
      this.logger.warn(`Already polling queue: ${queueName}`);
      return;
    }

    try {
      const queueUrl = await this.sqsService.getQueueUrl(queueName);
      this.isPolling.set(queueName, true);

      const poll = async () => {
        if (!this.isPolling.get(queueName)) {
          return;
        }

        try {
          const messages = await this.sqsService.receiveMessages(
            queueUrl,
            maxMessages,
            waitTimeSeconds,
          );

          if (messages.length > 0) {
            this.logger.log(`Received ${messages.length} messages from ${queueName}`);
            
            // Process messages
            await messageHandler(messages);

            // Auto-delete processed messages if enabled
            if (autoDelete) {
              await this.deleteProcessedMessages(queueUrl, messages);
            }
          }
        } catch (error) {
          this.logger.error(`Error polling queue ${queueName}:`, error);
        }

        // Schedule next poll
        if (this.isPolling.get(queueName)) {
          const timeoutId = setTimeout(poll, pollingInterval);
          this.pollingIntervals.set(queueName, timeoutId);
        }
      };

      // Start polling
      this.logger.log(`Starting to poll queue: ${queueName}`);
      poll();
    } catch (error) {
      this.logger.error(`Failed to start polling queue ${queueName}:`, error);
      this.isPolling.set(queueName, false);
      throw error;
    }
  }

  stopPolling(queueName: string): void {
    this.logger.log(`Stopping polling for queue: ${queueName}`);
    this.isPolling.set(queueName, false);
    
    const intervalId = this.pollingIntervals.get(queueName);
    if (intervalId) {
      clearTimeout(intervalId);
      this.pollingIntervals.delete(queueName);
    }
  }

  stopAllPolling(): void {
    this.logger.log('Stopping all polling operations');
    
    for (const queueName of this.isPolling.keys()) {
      this.stopPolling(queueName);
    }
  }

  isQueuePolling(queueName: string): boolean {
    return this.isPolling.get(queueName) || false;
  }

  getPollingQueues(): string[] {
    return Array.from(this.isPolling.keys()).filter(queueName => 
      this.isPolling.get(queueName)
    );
  }

  private async deleteProcessedMessages(queueUrl: string, messages: SqsMessage[]): Promise<void> {
    const deletePromises = messages
      .filter(message => message.receiptHandle)
      .map(message => 
        this.sqsService.deleteMessage(queueUrl, message.receiptHandle!)
      );

    try {
      await Promise.all(deletePromises);
      this.logger.log(`Deleted ${deletePromises.length} processed messages`);
    } catch (error) {
      this.logger.error('Failed to delete some processed messages:', error);
    }
  }
}
