import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsN<PERSON>ber, Min, Max } from 'class-validator';

export class SendMessageDto {
  @ApiProperty({
    description: 'The name of the SQS queue',
    example: 'my-queue',
  })
  @IsString()
  queueName: string;

  @ApiProperty({
    description: 'The message body to send',
    example: 'Hello, SQS!',
  })
  @IsString()
  messageBody: string;

  @ApiPropertyOptional({
    description: 'Delay in seconds before the message becomes available',
    minimum: 0,
    maximum: 900,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(900)
  delaySeconds?: number;

  @ApiPropertyOptional({
    description: 'Message group ID for FIFO queues',
    example: 'group-1',
  })
  @IsOptional()
  @IsString()
  messageGroupId?: string;

  @ApiPropertyOptional({
    description: 'Message deduplication ID for FIFO queues',
    example: 'dedup-1',
  })
  @IsOptional()
  @IsString()
  messageDeduplicationId?: string;
}
