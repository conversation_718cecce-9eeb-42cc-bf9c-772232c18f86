import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SendMessageResponseDto {
  @ApiProperty({
    description: 'The unique message ID assigned by SQS',
    example: '12345678-1234-1234-1234-123456789012',
  })
  messageId: string;

  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiPropertyOptional({
    description: 'Additional message or error details',
    example: 'Message sent successfully',
  })
  message?: string;
}

export class SqsMessageDto {
  @ApiProperty({
    description: 'The unique message ID',
    example: '12345678-1234-1234-1234-123456789012',
  })
  id: string;

  @ApiProperty({
    description: 'The message body',
    example: 'Hello, SQS!',
  })
  body: string;

  @ApiPropertyOptional({
    description: 'Receipt handle for message deletion',
    example: 'AQEBwJnKyrHigUMZj6rYigCgxlaS3SLy0a...',
  })
  receiptHandle?: string;

  @ApiPropertyOptional({
    description: 'Message attributes',
    example: { 'Author': '<PERSON>', 'Priority': 'High' },
  })
  attributes?: Record<string, string>;
}

export class ReceiveMessagesResponseDto {
  @ApiProperty({
    description: 'Array of received messages',
    type: [SqsMessageDto],
  })
  messages: SqsMessageDto[];

  @ApiProperty({
    description: 'Number of messages received',
    example: 3,
  })
  count: number;

  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;
}

export class PollingStatusResponseDto {
  @ApiProperty({
    description: 'Queue name',
    example: 'my-queue',
  })
  queueName: string;

  @ApiProperty({
    description: 'Whether the queue is currently being polled',
    example: true,
  })
  isPolling: boolean;

  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiPropertyOptional({
    description: 'Additional message',
    example: 'Polling started successfully',
  })
  message?: string;
}

export class PollingQueuesResponseDto {
  @ApiProperty({
    description: 'List of queues currently being polled',
    type: [String],
    example: ['queue-1', 'queue-2'],
  })
  pollingQueues: string[];

  @ApiProperty({
    description: 'Number of queues being polled',
    example: 2,
  })
  count: number;

  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;
}
