import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsN<PERSON>ber, Min, Max } from 'class-validator';

export class ReceiveMessagesDto {
  @ApiProperty({
    description: 'The name of the SQS queue',
    example: 'my-queue',
  })
  @IsString()
  queueName: string;

  @ApiPropertyOptional({
    description: 'Maximum number of messages to receive',
    minimum: 1,
    maximum: 10,
    default: 10,
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxMessages?: number;

  @ApiPropertyOptional({
    description: 'Wait time in seconds for long polling',
    minimum: 0,
    maximum: 20,
    default: 20,
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  waitTimeSeconds?: number;
}
