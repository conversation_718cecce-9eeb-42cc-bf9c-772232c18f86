// Module
export { SqsModule } from './sqs.module';

// Services
export { SqsService } from './services/sqs.service';
export { SqsPollingService } from './services/sqs-polling.service';

// Controllers
export { SqsProducerController } from './controllers/sqs-producer.controller';
export { SqsConsumerController } from './controllers/sqs-consumer.controller';

// DTOs
export { SendMessageDto } from './dto/send-message.dto';
export { ReceiveMessagesDto } from './dto/receive-messages.dto';
export { StartPollingDto, StopPollingDto } from './dto/polling-config.dto';
export {
  SendMessageResponseDto,
  SqsMessageDto,
  ReceiveMessagesResponseDto,
  PollingStatusResponseDto,
  PollingQueuesResponseDto,
} from './dto/response.dto';

// Types
export type { SqsMessage, SendMessageOptions } from './services/sqs.service';
export type { PollingConfig } from './services/sqs-polling.service';
