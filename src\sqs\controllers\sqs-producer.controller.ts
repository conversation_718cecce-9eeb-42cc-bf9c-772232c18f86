import { Body, Controller, HttpException, HttpStatus, Logger, Post } from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { SendMessageResponseDto } from '../dto/response.dto';
import { SendMessageDto } from '../dto/send-message.dto';
import { SqsService } from '../services/sqs.service';

@ApiTags('SQS Producer')
@Controller('sqs/producer')
export class SqsProducerController {
    private readonly logger = new Logger(SqsProducerController.name);

    constructor(private readonly sqsService: SqsService) {}

    @Post('send')
    @ApiOperation({
        summary: 'Send a message to SQS queue',
        description: 'Sends a message to the specified SQS queue with optional configuration parameters.',
    })
    @ApiResponse({
        status: 201,
        description: 'Message sent successfully',
        type: SendMessageResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Invalid request parameters',
    })
    @ApiInternalServerErrorResponse({
        description: 'Failed to send message to SQS',
    })
    async sendMessage(@Body() sendMessageDto: SendMessageDto): Promise<SendMessageResponseDto> {
        try {
            this.logger.log(`Sending message to queue: ${sendMessageDto.queueName}`);

            // Get queue URL
            const queueUrl = await this.sqsService.getQueueUrl(sendMessageDto.queueName);

            // Send message
            const messageId = await this.sqsService.sendMessage(queueUrl, sendMessageDto.messageBody, {
                delaySeconds: sendMessageDto.delaySeconds,
                messageGroupId: sendMessageDto.messageGroupId,
                messageDeduplicationId: sendMessageDto.messageDeduplicationId,
            });

            return {
                messageId,
                success: true,
                message: 'Message sent successfully',
            };
        } catch (error: any) {
            this.logger.error('Failed to send message:', error);

            throw new HttpException(
                {
                    success: false,
                    message: 'Failed to send message to SQS',
                    error: error.message,
                },
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }
}
