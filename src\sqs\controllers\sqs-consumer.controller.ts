import { Body, Controller, Get, HttpException, HttpStatus, Logger, Post, Query } from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiInternalServerErrorResponse,
    ApiOperation,
    ApiQuery,
    ApiResponse,
    ApiTags,
} from '@nestjs/swagger';
import { StartPollingDto, StopPollingDto } from '../dto/polling-config.dto';
import { ReceiveMessagesDto } from '../dto/receive-messages.dto';
import { PollingQueuesResponseDto, PollingStatusResponseDto, ReceiveMessagesResponseDto } from '../dto/response.dto';
import { SqsPollingService } from '../services/sqs-polling.service';
import { SqsService } from '../services/sqs.service';

@ApiTags('SQS Consumer')
@Controller('sqs/consumer')
export class SqsConsumerController {
    private readonly logger = new Logger(SqsConsumerController.name);

    constructor(
        private readonly sqsService: SqsService,
        private readonly sqsPollingService: SqsPollingService,
    ) {}

    @Post('receive')
    @ApiOperation({
        summary: 'Receive messages from SQS queue',
        description: 'Receives messages from the specified SQS queue using long polling.',
    })
    @ApiResponse({
        status: 200,
        description: 'Messages received successfully',
        type: ReceiveMessagesResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Invalid request parameters',
    })
    @ApiInternalServerErrorResponse({
        description: 'Failed to receive messages from SQS',
    })
    async receiveMessages(@Body() receiveMessagesDto: ReceiveMessagesDto): Promise<ReceiveMessagesResponseDto> {
        try {
            this.logger.log(`Receiving messages from queue: ${receiveMessagesDto.queueName}`);

            // Get queue URL
            const queueUrl = await this.sqsService.getQueueUrl(receiveMessagesDto.queueName);

            // Receive messages
            const messages = await this.sqsService.receiveMessages(
                queueUrl,
                receiveMessagesDto.maxMessages || 10,
                receiveMessagesDto.waitTimeSeconds || 20,
            );

            return {
                messages,
                count: messages.length,
                success: true,
            };
        } catch (error) {
            this.logger.error('Failed to receive messages:', error);

            throw new HttpException(
                {
                    success: false,
                    message: 'Failed to receive messages from SQS',
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Post('polling/start')
    @ApiOperation({
        summary: 'Start polling an SQS queue',
        description: 'Starts continuous polling of the specified SQS queue for new messages.',
    })
    @ApiResponse({
        status: 201,
        description: 'Polling started successfully',
        type: PollingStatusResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Invalid request parameters',
    })
    @ApiInternalServerErrorResponse({
        description: 'Failed to start polling',
    })
    async startPolling(@Body() startPollingDto: StartPollingDto): Promise<PollingStatusResponseDto> {
        try {
            this.logger.log(`Starting polling for queue: ${startPollingDto.queueName}`);

            // Default message handler that logs received messages
            const messageHandler = async (messages: any[]) => {
                this.logger.log(`Processed ${messages.length} messages from ${startPollingDto.queueName}`);
                messages.forEach((message: any, index: number) => {
                    this.logger.log(`Message ${index + 1}: ${message.body}`);
                });
            };

            await this.sqsPollingService.startPolling(
                {
                    queueName: startPollingDto.queueName,
                    maxMessages: startPollingDto.maxMessages,
                    waitTimeSeconds: startPollingDto.waitTimeSeconds,
                    pollingInterval: startPollingDto.pollingInterval,
                    autoDelete: startPollingDto.autoDelete,
                },
                messageHandler,
            );

            return {
                queueName: startPollingDto.queueName,
                isPolling: true,
                success: true,
                message: 'Polling started successfully',
            };
        } catch (error) {
            this.logger.error('Failed to start polling:', error);

            throw new HttpException(
                {
                    success: false,
                    message: 'Failed to start polling',
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Post('polling/stop')
    @ApiOperation({
        summary: 'Stop polling an SQS queue',
        description: 'Stops continuous polling of the specified SQS queue.',
    })
    @ApiResponse({
        status: 200,
        description: 'Polling stopped successfully',
        type: PollingStatusResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Invalid request parameters',
    })
    async stopPolling(@Body() stopPollingDto: StopPollingDto): Promise<PollingStatusResponseDto> {
        try {
            this.logger.log(`Stopping polling for queue: ${stopPollingDto.queueName}`);

            this.sqsPollingService.stopPolling(stopPollingDto.queueName);

            return {
                queueName: stopPollingDto.queueName,
                isPolling: false,
                success: true,
                message: 'Polling stopped successfully',
            };
        } catch (error) {
            this.logger.error('Failed to stop polling:', error);

            throw new HttpException(
                {
                    success: false,
                    message: 'Failed to stop polling',
                    error: error instanceof Error ? error.message : 'Unknown error',
                },
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
    }

    @Get('polling/status')
    @ApiOperation({
        summary: 'Get polling status for a queue',
        description: 'Returns the current polling status for the specified SQS queue.',
    })
    @ApiQuery({
        name: 'queueName',
        description: 'The name of the SQS queue',
        example: 'my-queue',
    })
    @ApiResponse({
        status: 200,
        description: 'Polling status retrieved successfully',
        type: PollingStatusResponseDto,
    })
    @ApiBadRequestResponse({
        description: 'Missing queue name parameter',
    })
    async getPollingStatus(@Query('queueName') queueName: string): Promise<PollingStatusResponseDto> {
        if (!queueName) {
            throw new HttpException(
                {
                    success: false,
                    message: 'Queue name is required',
                },
                HttpStatus.BAD_REQUEST,
            );
        }

        const isPolling = this.sqsPollingService.isQueuePolling(queueName);

        return {
            queueName,
            isPolling,
            success: true,
            message: isPolling ? 'Queue is being polled' : 'Queue is not being polled',
        };
    }

    @Get('polling/queues')
    @ApiOperation({
        summary: 'Get all queues being polled',
        description: 'Returns a list of all SQS queues currently being polled.',
    })
    @ApiResponse({
        status: 200,
        description: 'Polling queues retrieved successfully',
        type: PollingQueuesResponseDto,
    })
    async getPollingQueues(): Promise<PollingQueuesResponseDto> {
        const pollingQueues = this.sqsPollingService.getPollingQueues();

        return {
            pollingQueues,
            count: pollingQueues.length,
            success: true,
        };
    }
}
