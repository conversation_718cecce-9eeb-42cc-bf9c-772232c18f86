# SQS Module for NestJS

This module provides a comprehensive solution for working with AWS SQS (Simple Queue Service) in NestJS applications. It includes both producer and consumer functionality with OpenAPI documentation.

## Features

- **SQS Producer**: Send messages to SQS queues
- **SQS Consumer**: Receive messages from SQS queues
- **Polling Service**: Continuous polling of SQS queues
- **OpenAPI Documentation**: Fully documented API endpoints
- **Validation**: Request validation using class-validator
- **Error Handling**: Comprehensive error handling and logging

## Setup

### 1. Environment Variables

Create a `.env` file based on `.env.example`:

```bash
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
PORT=3000
```

### 2. Install Dependencies

The following dependencies are already installed:
- `@aws-sdk/client-sqs`
- `@nestjs/swagger`
- `swagger-ui-express`
- `class-validator`
- `class-transformer`

### 3. Start the Application

```bash
npm run start:dev
```

The application will be available at:
- API: `http://localhost:3000`
- Swagger Documentation: `http://localhost:3000/api`

## API Endpoints

### SQS Producer

#### Send Message
- **POST** `/sqs/producer/send`
- Sends a message to an SQS queue

**Request Body:**
```json
{
  "queueName": "my-queue",
  "messageBody": "Hello, SQS!",
  "delaySeconds": 0,
  "messageGroupId": "group-1",
  "messageDeduplicationId": "dedup-1"
}
```

### SQS Consumer

#### Receive Messages
- **POST** `/sqs/consumer/receive`
- Receives messages from an SQS queue

**Request Body:**
```json
{
  "queueName": "my-queue",
  "maxMessages": 10,
  "waitTimeSeconds": 20
}
```

#### Start Polling
- **POST** `/sqs/consumer/polling/start`
- Starts continuous polling of an SQS queue

**Request Body:**
```json
{
  "queueName": "my-queue",
  "maxMessages": 10,
  "waitTimeSeconds": 20,
  "pollingInterval": 1000,
  "autoDelete": true
}
```

#### Stop Polling
- **POST** `/sqs/consumer/polling/stop`
- Stops polling of an SQS queue

**Request Body:**
```json
{
  "queueName": "my-queue"
}
```

#### Get Polling Status
- **GET** `/sqs/consumer/polling/status?queueName=my-queue`
- Returns the polling status for a specific queue

#### Get All Polling Queues
- **GET** `/sqs/consumer/polling/queues`
- Returns all queues currently being polled

## Module Structure

```
src/sqs/
├── controllers/
│   ├── sqs-producer.controller.ts    # Producer endpoints
│   └── sqs-consumer.controller.ts    # Consumer endpoints
├── services/
│   ├── sqs.service.ts                # Core SQS operations
│   └── sqs-polling.service.ts        # Polling functionality
├── dto/
│   ├── send-message.dto.ts           # Send message request DTO
│   ├── receive-messages.dto.ts       # Receive messages request DTO
│   ├── polling-config.dto.ts         # Polling configuration DTOs
│   └── response.dto.ts               # Response DTOs
├── sqs.module.ts                     # Module definition
└── index.ts                          # Exports
```

## Usage Examples

### Sending a Message

```typescript
// Using the API
const response = await fetch('http://localhost:3000/sqs/producer/send', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    queueName: 'my-queue',
    messageBody: 'Hello, SQS!'
  })
});
```

### Starting Polling

```typescript
// Using the API
const response = await fetch('http://localhost:3000/sqs/consumer/polling/start', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    queueName: 'my-queue',
    maxMessages: 5,
    pollingInterval: 5000,
    autoDelete: true
  })
});
```

## Configuration

### AWS Credentials

The module uses AWS SDK v3 and supports multiple credential providers:
1. Environment variables (`AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`)
2. AWS credentials file
3. IAM roles (when running on EC2)
4. AWS SSO

### Queue Types

The module supports both standard and FIFO queues:
- **Standard queues**: Use `queueName` without `.fifo` suffix
- **FIFO queues**: Use `queueName` with `.fifo` suffix and provide `messageGroupId`

## Error Handling

All endpoints include comprehensive error handling:
- Input validation errors (400 Bad Request)
- AWS service errors (500 Internal Server Error)
- Detailed error messages in responses
- Structured logging

## Logging

The module includes structured logging for:
- Message sending/receiving operations
- Polling start/stop events
- Error conditions
- Performance metrics

## Security Considerations

1. **AWS Credentials**: Never commit AWS credentials to version control
2. **Queue Permissions**: Ensure proper IAM permissions for SQS operations
3. **Input Validation**: All inputs are validated using class-validator
4. **Error Messages**: Sensitive information is not exposed in error responses
