import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsN<PERSON>ber, IsBoolean, Min, Max } from 'class-validator';

export class StartPollingDto {
  @ApiProperty({
    description: 'The name of the SQS queue to poll',
    example: 'my-queue',
  })
  @IsString()
  queueName: string;

  @ApiPropertyOptional({
    description: 'Maximum number of messages to receive per poll',
    minimum: 1,
    maximum: 10,
    default: 10,
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxMessages?: number;

  @ApiPropertyOptional({
    description: 'Wait time in seconds for long polling',
    minimum: 0,
    maximum: 20,
    default: 20,
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  waitTimeSeconds?: number;

  @ApiPropertyOptional({
    description: 'Interval in milliseconds between polling attempts',
    minimum: 100,
    default: 1000,
    example: 5000,
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  pollingInterval?: number;

  @ApiPropertyOptional({
    description: 'Whether to automatically delete messages after processing',
    default: true,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  autoDelete?: boolean;
}

export class StopPollingDto {
  @ApiProperty({
    description: 'The name of the SQS queue to stop polling',
    example: 'my-queue',
  })
  @IsString()
  queueName: string;
}
