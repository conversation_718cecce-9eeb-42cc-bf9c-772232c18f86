import { Injectable, Logger } from '@nestjs/common';
import {
  SQSClient,
  SendMessageCommand,
  ReceiveMessageCommand,
  DeleteMessageCommand,
  GetQueueUrlCommand,
  Message,
} from '@aws-sdk/client-sqs';

export interface SqsMessage {
  id: string;
  body: string;
  receiptHandle?: string;
  attributes?: Record<string, string>;
}

export interface SendMessageOptions {
  delaySeconds?: number;
  messageGroupId?: string;
  messageDeduplicationId?: string;
}

@Injectable()
export class SqsService {
  private readonly logger = new Logger(SqsService.name);
  private readonly sqsClient: SQSClient;

  constructor() {
    this.sqsClient = new SQSClient({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });
  }

  async getQueueUrl(queueName: string): Promise<string> {
    try {
      const command = new GetQueueUrlCommand({ QueueName: queueName });
      const response = await this.sqsClient.send(command);
      return response.QueueUrl!;
    } catch (error) {
      this.logger.error(`Failed to get queue URL for ${queueName}`, error);
      throw error;
    }
  }

  async sendMessage(
    queueUrl: string,
    messageBody: string,
    options?: SendMessageOptions,
  ): Promise<string> {
    try {
      const command = new SendMessageCommand({
        QueueUrl: queueUrl,
        MessageBody: messageBody,
        DelaySeconds: options?.delaySeconds,
        MessageGroupId: options?.messageGroupId,
        MessageDeduplicationId: options?.messageDeduplicationId,
      });

      const response = await this.sqsClient.send(command);
      this.logger.log(`Message sent successfully: ${response.MessageId}`);
      return response.MessageId!;
    } catch (error) {
      this.logger.error('Failed to send message to SQS', error);
      throw error;
    }
  }

  async receiveMessages(
    queueUrl: string,
    maxMessages: number = 10,
    waitTimeSeconds: number = 20,
  ): Promise<SqsMessage[]> {
    try {
      const command = new ReceiveMessageCommand({
        QueueUrl: queueUrl,
        MaxNumberOfMessages: maxMessages,
        WaitTimeSeconds: waitTimeSeconds,
        MessageAttributeNames: ['All'],
      });

      const response = await this.sqsClient.send(command);
      
      if (!response.Messages) {
        return [];
      }

      return response.Messages.map((message: Message) => ({
        id: message.MessageId!,
        body: message.Body!,
        receiptHandle: message.ReceiptHandle,
        attributes: message.MessageAttributes
          ? Object.fromEntries(
              Object.entries(message.MessageAttributes).map(([key, value]) => [
                key,
                value.StringValue || '',
              ]),
            )
          : {},
      }));
    } catch (error) {
      this.logger.error('Failed to receive messages from SQS', error);
      throw error;
    }
  }

  async deleteMessage(queueUrl: string, receiptHandle: string): Promise<void> {
    try {
      const command = new DeleteMessageCommand({
        QueueUrl: queueUrl,
        ReceiptHandle: receiptHandle,
      });

      await this.sqsClient.send(command);
      this.logger.log('Message deleted successfully');
    } catch (error) {
      this.logger.error('Failed to delete message from SQS', error);
      throw error;
    }
  }
}
