import { Module } from '@nestjs/common';
import { SqsProducerController } from './controllers/sqs-producer.controller';
import { SqsConsumerController } from './controllers/sqs-consumer.controller';
import { SqsService } from './services/sqs.service';
import { SqsPollingService } from './services/sqs-polling.service';

@Module({
  controllers: [SqsProducerController, SqsConsumerController],
  providers: [SqsService, SqsPollingService],
  exports: [SqsService, SqsPollingService],
})
export class SqsModule {}
